'use client'

import Link from 'next/link'
import { AppShell, Avatar, Box, Burger, Group, Image, Menu, Title } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { IconBell, IconLogout, IconSettings, IconUser } from '@tabler/icons-react'

const user = {
  name: 'System',
  avatar: '/system.svg',
}

export function SidebarLayout() {
  const [opened, { toggle }] = useDisclosure()

  return (
    <AppShell
      header={{ height: '4rem' }}
      navbar={{
        width: '16rem',
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="sm"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <Link href="/">
              <Group gap="xs">
                <Image src="/logo.svg" alt="Logo" w={32} h={32} />
                <Title size="h3">Mantine</Title>
              </Group>
            </Link>
          </Group>
          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <Avatar src={user.avatar} radius="xl">
                {user.name.slice(0, 2).toUpperCase()}
              </Avatar>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>Account</Menu.Label>
              <Menu.Item leftSection={<IconUser size={16} />}>Profile</Menu.Item>
              <Menu.Item leftSection={<IconSettings size={16} />}>Settings</Menu.Item>
              <Menu.Item leftSection={<IconBell size={16} />}>Notifications</Menu.Item>
              <Menu.Divider />
              <Menu.Item leftSection={<IconLogout size={16} />} color="red">
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar></AppShell.Navbar>

      <AppShell.Main>
        <Box>{/* Main content area */}</Box>
      </AppShell.Main>
    </AppShell>
  )
}
